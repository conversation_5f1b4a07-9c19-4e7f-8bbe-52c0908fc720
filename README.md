# nnU-Net pour la Segmentation d'Images

## Description
Ce projet implémente la segmentation d'images en utilisant nnU-Net, un framework de deep learning spécialisé dans la segmentation d'images médicales. nnU-Net est une implémentation robuste et automatisée de l'architecture U-Net qui a démontré d'excellentes performances dans diverses tâches de segmentation.

## Structure du Projet
```
unet/
├── utils/             # Fonctions utilitaires
├── train_nnunet.py    # Script d'entraînement nnU-Net
├── infer_nnunet.py    # Script d'inférence nnU-Net
├── visualize_mask.py  # Visualisation des masques de segmentation
├── visualize_labels.py # Visualisation des labels
└── visualise_10_mask.py # Visualisation de 10 masques
```

## Prérequis
- Python 3.8+
- nnU-Net
- PyTorch
- NumPy
- Matplotlib
- OpenCV

## Installation
```bash
pip install -r requirements.txt
```

## Utilisation

### Entraînement
```bash
python train_nnunet.py --input_dir path/to/data --output_dir path/to/output
```

### Inférence
```bash
python infer_nnunet.py --input_dir path/to/test/images --output_dir path/to/predictions
```

### Visualisation
```bash
python visualize_mask.py --image_path path/to/image --mask_path path/to/mask
python visualize_labels.py --image_path path/to/image --labels_path path/to/labels
python visualise_10_mask.py --data_dir path/to/data
```

## Fonctionnalités
- Implémentation nnU-Net pour la segmentation d'images
- Pipeline d'entraînement automatisé
- Outils de visualisation des résultats
- Support pour l'inférence sur de nouvelles images
- Visualisation des masques et des labels

## Support
Pour toute question ou problème, veuillez créer une issue dans le repository GitLab.

## Licence
Propriétaire - Tous droits réservés
